{"version": 2, "name": "viewdeletedreddit", "alias": ["viewdeletedreddit.com", "www.viewdeletedreddit.com"], "regions": ["iad1", "sfo1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NODE_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}, {"key": "Cache-Control", "value": "s-maxage=60, stale-while-revalidate"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "s-maxage=86400, stale-while-revalidate"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}, {"key": "Cache-Control", "value": "s-maxage=86400, stale-while-revalidate"}]}, {"source": "/(.*\\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "redirects": [{"source": "/www.viewdeletedreddit.com/(.*)", "destination": "https://viewdeletedreddit.com/$1", "permanent": true}], "rewrites": [{"source": "/api/health", "destination": "/api/lookup"}]}