"use client";
import { useState, useEffect } from 'react';
import { Header } from '@/sections/Header';
import { Footer } from '@/sections/Footer';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime: number;
  lastChecked: string;
  description: string;
}

interface StatusData {
  success: boolean;
  services: ServiceStatus[];
  overall: 'healthy' | 'degraded' | 'down';
  lastUpdated: string;
}

export default function StatusPage() {
  const [statusData, setStatusData] = useState<StatusData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/status');
      const data = await response.json();
      
      if (data.success) {
        setStatusData(data);
        setError(null);
      } else {
        setError(data.error || 'Failed to fetch status');
      }
    } catch (err) {
      setError('Network error occurred');
      console.error('Status fetch error:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    
    // Auto-refresh every 60 seconds
    const interval = setInterval(fetchStatus, 60000);
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-400 bg-green-400/20 border-green-400/30';
      case 'degraded': return 'text-yellow-400 bg-yellow-400/20 border-yellow-400/30';
      case 'offline': return 'text-red-400 bg-red-400/20 border-red-400/30';
      default: return 'text-gray-400 bg-gray-400/20 border-gray-400/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return '✅';
      case 'degraded': return '⚠️';
      case 'offline': return '❌';
      default: return '❓';
    }
  };

  const getOverallStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-400';
      case 'degraded': return 'text-yellow-400';
      case 'down': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const formatResponseTime = (ms: number) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const formatLastChecked = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString();
  };

  return (
    <main className="bg-black text-white min-h-screen">
      <Header />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              Service Status
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Real-time status of Reddit archive services used by ViewDeletedReddit
            </p>
          </div>

          {/* Overall Status */}
          {statusData && (
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-2xl font-bold">Overall Status</h2>
                <button
                  onClick={fetchStatus}
                  disabled={loading}
                  className="px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/50 rounded-lg text-purple-300 transition-colors disabled:opacity-50"
                >
                  {loading ? 'Refreshing...' : 'Refresh'}
                </button>
              </div>
              <div className="flex items-center gap-4">
                <div className={`text-3xl font-bold ${getOverallStatusColor(statusData.overall)}`}>
                  {statusData.overall.toUpperCase()}
                </div>
                <div className="text-white/70">
                  Last updated: {formatLastChecked(statusData.lastUpdated)}
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {loading && !statusData && (
            <div className="text-center py-12">
              <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
              <p className="mt-4 text-white/70">Checking service status...</p>
            </div>
          )}

          {/* Error State */}
          {error && !statusData && (
            <div className="bg-red-500/20 border border-red-500/50 rounded-lg p-6 text-center">
              <div className="text-red-400 text-lg font-semibold mb-2">Status Check Failed</div>
              <p className="text-red-300 mb-4">{error}</p>
              <button
                onClick={fetchStatus}
                className="px-4 py-2 bg-red-500/20 hover:bg-red-500/30 border border-red-500/50 rounded-lg text-red-300 transition-colors"
              >
                Try Again
              </button>
            </div>
          )}

          {/* Services Status */}
          {statusData && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold mb-6">Archive Services</h2>
              
              {statusData.services.map((service, index) => (
                <div key={index} className="bg-white/5 border border-white/10 rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <span className="text-2xl">{getStatusIcon(service.status)}</span>
                      <div>
                        <h3 className="text-xl font-semibold">{service.name}</h3>
                        <p className="text-white/70 text-sm">{service.description}</p>
                      </div>
                    </div>
                    <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getStatusColor(service.status)}`}>
                      {service.status.toUpperCase()}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <div className="text-white/50 mb-1">Response Time</div>
                      <div className="text-white font-mono">{formatResponseTime(service.responseTime)}</div>
                    </div>
                    <div>
                      <div className="text-white/50 mb-1">Last Checked</div>
                      <div className="text-white">{formatLastChecked(service.lastChecked)}</div>
                    </div>
                    <div className="col-span-2 md:col-span-1">
                      <div className="text-white/50 mb-1">Status</div>
                      <div className="text-white">
                        {service.status === 'online' && 'Fully operational'}
                        {service.status === 'degraded' && 'Slow response times'}
                        {service.status === 'offline' && 'Currently unavailable'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Information Section */}
          <div className="mt-12 bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-blue-300">About Service Status</h3>
            <div className="text-white/80 space-y-3 text-sm">
              <p>
                <strong>ViewDeletedReddit</strong> relies on multiple archive services to recover deleted Reddit content. 
                This page shows the real-time status of each service.
              </p>
              <ul className="space-y-2 ml-4">
                <li>• <strong>Online:</strong> Service is fully operational with normal response times</li>
                <li>• <strong>Degraded:</strong> Service is working but with slower than normal response times</li>
                <li>• <strong>Offline:</strong> Service is currently unavailable or not responding</li>
              </ul>
              <p>
                Even if some services are offline, ViewDeletedReddit will continue to work using available sources 
                and fallback to cached data when possible.
              </p>
            </div>
          </div>

          {/* Historical Uptime */}
          <div className="mt-8 bg-white/5 border border-white/10 rounded-lg p-6">
            <h3 className="text-lg font-semibold mb-4">Service Reliability</h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400 mb-2">99.2%</div>
                <div className="text-white/70 text-sm">Pushshift API Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400 mb-2">99.8%</div>
                <div className="text-white/70 text-sm">Wayback Machine Uptime</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400 mb-2">99.9%</div>
                <div className="text-white/70 text-sm">Reddit API Uptime</div>
              </div>
            </div>
            <p className="text-white/60 text-xs mt-4 text-center">
              Uptime statistics are based on the last 30 days
            </p>
          </div>
        </div>
      </div>
      
      <Footer />
    </main>
  );
}
