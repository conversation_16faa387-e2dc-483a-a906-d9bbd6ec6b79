import { Metadata } from 'next';
import { Header } from '@/sections/Header';
import { Footer } from '@/sections/Footer';

export const metadata: Metadata = {
  title: 'How to See Deleted Reddit Posts - Complete Guide | ViewDeletedReddit',
  description: 'Learn how to see deleted Reddit posts and comments using ViewDeletedReddit. Access archived content from Pushshift, Wayback Machine, and other sources.',
  keywords: 'how to see deleted reddit posts, view deleted reddit comments, reddit archive, pushshift, wayback machine, deleted reddit content',
  openGraph: {
    title: 'How to See Deleted Reddit Posts - Complete Guide',
    description: 'Learn how to see deleted Reddit posts and comments using ViewDeletedReddit. Access archived content from multiple sources.',
    url: 'https://ViewDeletedReddit.com/how-to-see-deleted-reddit-posts',
  },
};

export default function HowToSeeDeletedRedditPosts() {
  return (
    <main className="bg-black text-white min-h-screen">
      <Header />
      
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Hero Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
              How to See Deleted Reddit Posts
            </h1>
            <p className="text-xl text-white/80 max-w-3xl mx-auto">
              Complete guide to viewing deleted Reddit posts and comments using ViewDeletedReddit and other archive tools
            </p>
          </div>

          {/* Quick Start */}
          <div className="bg-gradient-to-r from-purple-900/20 to-pink-900/20 border border-purple-500/30 rounded-lg p-8 mb-12">
            <h2 className="text-2xl font-bold mb-4 text-purple-300">🚀 Quick Start</h2>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">📋</span>
                </div>
                <h3 className="font-semibold mb-2">1. Copy URL</h3>
                <p className="text-white/70 text-sm">Copy the Reddit post or comment URL</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">🔍</span>
                </div>
                <h3 className="font-semibold mb-2">2. Search</h3>
                <p className="text-white/70 text-sm">Paste URL into ViewDeletedReddit search</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-3">
                  <span className="text-2xl">✨</span>
                </div>
                <h3 className="font-semibold mb-2">3. View</h3>
                <p className="text-white/70 text-sm">Access recovered deleted content</p>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="prose prose-invert max-w-none">
            
            <h2 className="text-3xl font-bold mb-6">Why Reddit Posts Get Deleted</h2>
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-8">
              <p className="text-white/80 mb-4">Reddit posts and comments can be deleted for several reasons:</p>
              <ul className="text-white/80 space-y-2">
                <li><strong>User deletion:</strong> Authors delete their own posts or comments</li>
                <li><strong>Moderator removal:</strong> Content violates subreddit rules</li>
                <li><strong>Admin removal:</strong> Content violates Reddit's site-wide policies</li>
                <li><strong>Account deletion:</strong> When users delete their accounts, their content may disappear</li>
                <li><strong>Shadowbanning:</strong> Content becomes invisible to other users</li>
              </ul>
            </div>

            <h2 className="text-3xl font-bold mb-6">Methods to See Deleted Reddit Posts</h2>

            <h3 className="text-2xl font-semibold mb-4 text-purple-300">Method 1: ViewDeletedReddit (Recommended)</h3>
            <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-6 mb-6">
              <p className="text-white/80 mb-4">
                <strong>ViewDeletedReddit.com</strong> is the most comprehensive tool for viewing deleted Reddit content:
              </p>
              <ul className="text-white/80 space-y-2 mb-4">
                <li>✅ Searches multiple archive sources simultaneously</li>
                <li>✅ Works with both post URLs and usernames</li>
                <li>✅ Shows source attribution for recovered content</li>
                <li>✅ Mobile-friendly interface</li>
                <li>✅ No registration required</li>
              </ul>
              <div className="bg-black/30 rounded p-4">
                <p className="text-sm text-white/70 mb-2">Example usage:</p>
                <code className="text-purple-300">
                  https://www.reddit.com/r/AskReddit/comments/abc123/example_post/
                </code>
              </div>
            </div>

            <h3 className="text-2xl font-semibold mb-4 text-purple-300">Method 2: Archive Sources</h3>
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h4 className="text-lg font-semibold mb-3 text-blue-300">Pushshift Archives</h4>
                <p className="text-white/80 text-sm mb-3">
                  Comprehensive Reddit archive with historical data from 2005 onwards.
                </p>
                <ul className="text-white/70 text-sm space-y-1">
                  <li>• Real-time archiving (when operational)</li>
                  <li>• API access available</li>
                  <li>• Searchable by multiple parameters</li>
                </ul>
              </div>
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h4 className="text-lg font-semibold mb-3 text-orange-300">Wayback Machine</h4>
                <p className="text-white/80 text-sm mb-3">
                  Internet Archive snapshots of Reddit pages over time.
                </p>
                <ul className="text-white/70 text-sm space-y-1">
                  <li>• Historical snapshots</li>
                  <li>• Reliable and stable</li>
                  <li>• Full page context preserved</li>
                </ul>
              </div>
            </div>

            <h2 className="text-3xl font-bold mb-6">Step-by-Step Guide</h2>
            
            <h3 className="text-xl font-semibold mb-4">For Specific Posts/Comments:</h3>
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-6">
              <ol className="text-white/80 space-y-3">
                <li><strong>1. Get the Reddit URL</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• Copy the full URL from your browser</li>
                    <li>• Or right-click and "Copy link address"</li>
                    <li>• Works with both post and comment URLs</li>
                  </ul>
                </li>
                <li><strong>2. Visit ViewDeletedReddit.com</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• Go to the homepage</li>
                    <li>• Paste the URL in the search box</li>
                    <li>• Click "Search" or press Enter</li>
                  </ul>
                </li>
                <li><strong>3. Review Results</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• Browse recovered posts and comments</li>
                    <li>• Check source attribution</li>
                    <li>• Click "View on Reddit" to see original context</li>
                  </ul>
                </li>
              </ol>
            </div>

            <h3 className="text-xl font-semibold mb-4">For User Content:</h3>
            <div className="bg-white/5 border border-white/10 rounded-lg p-6 mb-8">
              <ol className="text-white/80 space-y-3">
                <li><strong>1. Enter Username</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• Type the Reddit username (with or without u/)</li>
                    <li>• Example: "spez" or "u/spez"</li>
                  </ul>
                </li>
                <li><strong>2. Search Archives</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• ViewDeletedReddit searches all available archives</li>
                    <li>• Results show posts and comments by that user</li>
                  </ul>
                </li>
                <li><strong>3. Filter Results</strong>
                  <ul className="mt-2 ml-4 space-y-1 text-white/70">
                    <li>• Switch between "Posts" and "Comments" tabs</li>
                    <li>• Sort by date or relevance</li>
                  </ul>
                </li>
              </ol>
            </div>

            <h2 className="text-3xl font-bold mb-6">Tips for Better Results</h2>
            <div className="grid md:grid-cols-2 gap-6 mb-8">
              <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-6">
                <h4 className="text-blue-300 font-semibold mb-3">✅ Best Practices</h4>
                <ul className="text-white/80 space-y-2 text-sm">
                  <li>• Use complete Reddit URLs for best results</li>
                  <li>• Try searching soon after content is deleted</li>
                  <li>• Check multiple archive sources</li>
                  <li>• Search by username for comprehensive results</li>
                </ul>
              </div>
              <div className="bg-yellow-900/20 border border-yellow-500/30 rounded-lg p-6">
                <h4 className="text-yellow-300 font-semibold mb-3">⚠️ Limitations</h4>
                <ul className="text-white/80 space-y-2 text-sm">
                  <li>• Very recent deletions may not be archived</li>
                  <li>• Some content may never have been archived</li>
                  <li>• Archive APIs may have temporary outages</li>
                  <li>• Private subreddit content is not archived</li>
                </ul>
              </div>
            </div>

            <h2 className="text-3xl font-bold mb-6">Frequently Asked Questions</h2>
            <div className="space-y-6 mb-8">
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h4 className="font-semibold mb-2 text-purple-300">Can I see all deleted Reddit posts?</h4>
                <p className="text-white/80 text-sm">
                  Not all deleted content can be recovered. Success depends on whether the content was archived before deletion and the availability of archive services.
                </p>
              </div>
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h4 className="font-semibold mb-2 text-purple-300">Is it legal to view deleted Reddit posts?</h4>
                <p className="text-white/80 text-sm">
                  Yes, viewing archived public Reddit content is legal. The content was publicly posted and archived by third-party services.
                </p>
              </div>
              <div className="bg-white/5 border border-white/10 rounded-lg p-6">
                <h4 className="font-semibold mb-2 text-purple-300">How recent can deleted content be recovered?</h4>
                <p className="text-white/80 text-sm">
                  This depends on archive frequency. Some services archive in real-time, while others may have delays of hours or days.
                </p>
              </div>
            </div>

            {/* CTA Section */}
            <div className="bg-gradient-to-r from-purple-900/30 to-pink-900/30 border border-purple-500/30 rounded-lg p-8 text-center">
              <h3 className="text-2xl font-bold mb-4">Ready to Recover Deleted Reddit Content?</h3>
              <p className="text-white/80 mb-6">
                Try ViewDeletedReddit now - the fastest way to see deleted Reddit posts and comments
              </p>
              <a 
                href="/"
                className="inline-block bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold px-8 py-3 rounded-lg hover:from-purple-600 hover:to-pink-600 transition-all duration-200"
              >
                Start Searching →
              </a>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </main>
  );
}
