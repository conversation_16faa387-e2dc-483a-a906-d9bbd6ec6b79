import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

interface ServiceStatus {
  name: string;
  status: 'online' | 'offline' | 'degraded';
  responseTime: number;
  lastChecked: string;
  description: string;
}

interface StatusResponse {
  success: boolean;
  services: ServiceStatus[];
  overall: 'healthy' | 'degraded' | 'down';
  lastUpdated: string;
}

async function checkPushshiftAPI(): Promise<ServiceStatus> {
  const startTime = Date.now();
  try {
    const response = await axios.get('https://api.pullpush.io/reddit/search/submission/?size=1', {
      timeout: 5000,
      headers: {
        'User-Agent': 'ViewDeletedReddit/1.0'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.status === 200 && response.data) {
      return {
        name: 'Pushshift API',
        status: responseTime > 3000 ? 'degraded' : 'online',
        responseTime,
        lastChecked: new Date().toISOString(),
        description: 'Reddit archive API for posts and comments'
      };
    } else {
      throw new Error('Invalid response');
    }
  } catch (error) {
    return {
      name: 'Pushshift API',
      status: 'offline',
      responseTime: Date.now() - startTime,
      lastChecked: new Date().toISOString(),
      description: 'Reddit archive API for posts and comments'
    };
  }
}

async function checkWaybackMachine(): Promise<ServiceStatus> {
  const startTime = Date.now();
  try {
    const testUrl = 'https://web.archive.org/cdx/search/cdx?url=reddit.com&limit=1&output=json';
    const response = await axios.get(testUrl, {
      timeout: 8000,
      headers: {
        'User-Agent': 'ViewDeletedReddit/1.0'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.status === 200) {
      return {
        name: 'Wayback Machine',
        status: responseTime > 5000 ? 'degraded' : 'online',
        responseTime,
        lastChecked: new Date().toISOString(),
        description: 'Internet Archive snapshots of Reddit pages'
      };
    } else {
      throw new Error('Invalid response');
    }
  } catch (error) {
    return {
      name: 'Wayback Machine',
      status: 'offline',
      responseTime: Date.now() - startTime,
      lastChecked: new Date().toISOString(),
      description: 'Internet Archive snapshots of Reddit pages'
    };
  }
}

async function checkRedditAPI(): Promise<ServiceStatus> {
  const startTime = Date.now();
  try {
    const response = await axios.get('https://www.reddit.com/r/test.json?limit=1', {
      timeout: 5000,
      headers: {
        'User-Agent': 'ViewDeletedReddit/1.0'
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    if (response.status === 200 && response.data) {
      return {
        name: 'Reddit JSON API',
        status: responseTime > 3000 ? 'degraded' : 'online',
        responseTime,
        lastChecked: new Date().toISOString(),
        description: 'Official Reddit API for current content'
      };
    } else {
      throw new Error('Invalid response');
    }
  } catch (error) {
    return {
      name: 'Reddit JSON API',
      status: 'offline',
      responseTime: Date.now() - startTime,
      lastChecked: new Date().toISOString(),
      description: 'Official Reddit API for current content'
    };
  }
}

function calculateOverallStatus(services: ServiceStatus[]): 'healthy' | 'degraded' | 'down' {
  const onlineCount = services.filter(s => s.status === 'online').length;
  const degradedCount = services.filter(s => s.status === 'degraded').length;
  const offlineCount = services.filter(s => s.status === 'offline').length;
  
  if (onlineCount === services.length) {
    return 'healthy';
  } else if (onlineCount + degradedCount > 0) {
    return 'degraded';
  } else {
    return 'down';
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check all services in parallel
    const [pushshiftStatus, waybackStatus, redditStatus] = await Promise.all([
      checkPushshiftAPI(),
      checkWaybackMachine(),
      checkRedditAPI()
    ]);
    
    const services = [pushshiftStatus, waybackStatus, redditStatus];
    const overall = calculateOverallStatus(services);
    
    const response: StatusResponse = {
      success: true,
      services,
      overall,
      lastUpdated: new Date().toISOString()
    };
    
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=60', // Cache for 1 minute
      }
    });
    
  } catch (error) {
    console.error('Status check error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check service status',
      services: [],
      overall: 'down',
      lastUpdated: new Date().toISOString()
    }, { status: 500 });
  }
}

// Handle POST requests (same as GET for this endpoint)
export async function POST(request: NextRequest) {
  return GET(request);
}
