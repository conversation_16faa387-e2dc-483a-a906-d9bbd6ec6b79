import { NextRequest, NextResponse } from 'next/server';
import axios from 'axios';

// Types for our API responses
interface RedditPost {
  id: string;
  title: string;
  selftext: string;
  author: string;
  created_utc: number;
  subreddit: string;
  url: string;
  score: number;
  num_comments: number;
  permalink: string;
  is_deleted: boolean;
  source: 'pushshift' | 'wayback' | 'reddit';
}

interface RedditComment {
  id: string;
  body: string;
  author: string;
  created_utc: number;
  subreddit: string;
  score: number;
  parent_id: string;
  link_id: string;
  permalink: string;
  is_deleted: boolean;
  source: 'pushshift' | 'wayback' | 'reddit';
}

interface APIResponse {
  success: boolean;
  data: {
    posts: RedditPost[];
    comments: RedditComment[];
    total_found: number;
    query_type: 'url' | 'username';
    query_value: string;
  };
  error?: string;
}

// Helper function to detect if input is a Reddit URL or username
function parseQuery(query: string): { type: 'url' | 'username'; value: string; postId?: string; subreddit?: string } {
  // Clean the query
  const cleanQuery = query.trim();

  // Check if it's a Reddit URL
  const redditUrlRegex = /(?:https?:\/\/)?(?:www\.)?reddit\.com\/r\/([^\/]+)\/comments\/([^\/]+)/i;
  const match = cleanQuery.match(redditUrlRegex);

  if (match) {
    return {
      type: 'url',
      value: cleanQuery,
      subreddit: match[1],
      postId: match[2]
    };
  }

  // Otherwise treat as username (remove u/ prefix if present)
  const username = cleanQuery.replace(/^u\//, '');
  return {
    type: 'username',
    value: username
  };
}

// Helper function to check if content is deleted/removed
function isContentDeleted(text: string, author: string): boolean {
  const deletedIndicators = ['[deleted]', '[removed]', '', null, undefined];
  return deletedIndicators.includes(text) || deletedIndicators.includes(author) || author === '[deleted]';
}

// Helper function to format recovered content
function formatRecoveredContent(originalContent: string, isDeleted: boolean, source: string): string {
  if (!isDeleted) return originalContent;

  if (!originalContent || originalContent === '[deleted]' || originalContent === '[removed]') {
    return `[Content was deleted/removed - recovered from ${source} archive]`;
  }

  return originalContent;
}

// Query alternative Reddit archives (Reveddit, Unddit, etc.)
async function queryAlternativeArchives(queryInfo: ReturnType<typeof parseQuery>): Promise<{ posts: RedditPost[]; comments: RedditComment[] }> {
  const posts: RedditPost[] = [];
  const comments: RedditComment[] = [];

  try {
    if (queryInfo.type === 'url' && queryInfo.postId && queryInfo.subreddit) {
      // Try to get data from Reddit's JSON API first (sometimes works for recently deleted posts)
      try {
        const redditJsonUrl = `https://www.reddit.com/r/${queryInfo.subreddit}/comments/${queryInfo.postId}.json`;
        const response = await axios.get(redditJsonUrl, {
          timeout: 5000,
          headers: {
            'User-Agent': 'ViewDeletedReddit/1.0'
          }
        });

        if (response.data && response.data.length > 0) {
          const postData = response.data[0]?.data?.children?.[0]?.data;
          const commentsData = response.data[1]?.data?.children || [];

          if (postData) {
            // Check if post is deleted or removed
            const isDeleted = postData.selftext === '[deleted]' || postData.selftext === '[removed]' ||
                             postData.author === '[deleted]' || postData.removed_by_category;

            posts.push({
              id: postData.id,
              title: postData.title || '[Deleted Post]',
              selftext: isDeleted ? '[This post was deleted/removed]' : (postData.selftext || ''),
              author: postData.author || '[deleted]',
              created_utc: postData.created_utc,
              subreddit: postData.subreddit,
              url: queryInfo.value,
              score: postData.score || 0,
              num_comments: postData.num_comments || 0,
              permalink: postData.permalink,
              is_deleted: isDeleted,
              source: 'reddit'
            });
          }

          // Process comments
          commentsData.forEach((commentItem: any) => {
            const comment = commentItem.data;
            if (comment && comment.id) {
              const isCommentDeleted = comment.body === '[deleted]' || comment.body === '[removed]' ||
                                     comment.author === '[deleted]';

              comments.push({
                id: comment.id,
                body: isCommentDeleted ? '[This comment was deleted/removed]' : (comment.body || ''),
                author: comment.author || '[deleted]',
                created_utc: comment.created_utc,
                subreddit: comment.subreddit,
                score: comment.score || 0,
                parent_id: comment.parent_id,
                link_id: comment.link_id,
                permalink: comment.permalink,
                is_deleted: isCommentDeleted,
                source: 'reddit'
              });
            }
          });
        }
      } catch (redditError) {
        console.log('Reddit JSON API failed, trying alternative sources...');
      }

      // Try Pushshift.io alternative endpoints
      try {
        const pushshiftUrl = `https://api.pullpush.io/reddit/search/submission/?ids=${queryInfo.postId}&size=1`;
        const pushshiftResponse = await axios.get(pushshiftUrl, {
          timeout: 8000,
          headers: {
            'User-Agent': 'ViewDeletedReddit/1.0'
          }
        });

        if (pushshiftResponse.data && pushshiftResponse.data.data && pushshiftResponse.data.data.length > 0) {
          const postData = pushshiftResponse.data.data[0];

          posts.push({
            id: postData.id,
            title: postData.title || '[Recovered Post]',
            selftext: postData.selftext || '[Content recovered from archive]',
            author: postData.author || '[deleted]',
            created_utc: postData.created_utc,
            subreddit: postData.subreddit,
            url: queryInfo.value,
            score: postData.score || 0,
            num_comments: postData.num_comments || 0,
            permalink: postData.permalink || `/r/${postData.subreddit}/comments/${postData.id}/`,
            is_deleted: true,
            source: 'pushshift'
          });
        }

        // Also try to get comments for this post
        const commentsUrl = `https://api.pullpush.io/reddit/search/comment/?link_id=${queryInfo.postId}&size=50&sort=desc`;
        const commentsResponse = await axios.get(commentsUrl, {
          timeout: 8000,
          headers: {
            'User-Agent': 'ViewDeletedReddit/1.0'
          }
        });

        if (commentsResponse.data && commentsResponse.data.data) {
          commentsResponse.data.data.forEach((comment: any) => {
            comments.push({
              id: comment.id,
              body: comment.body || '[Recovered comment]',
              author: comment.author || '[deleted]',
              created_utc: comment.created_utc,
              subreddit: comment.subreddit,
              score: comment.score || 0,
              parent_id: comment.parent_id,
              link_id: comment.link_id,
              permalink: comment.permalink || `/r/${comment.subreddit}/comments/${queryInfo.postId}/_/${comment.id}/`,
              is_deleted: true,
              source: 'pushshift'
            });
          });
        }

      } catch (pushshiftError) {
        console.log('Pushshift alternative API failed:', pushshiftError.message);
      }
    }

    // For username queries, try to get user's posts and comments
    if (queryInfo.type === 'username') {
      try {
        const userPostsUrl = `https://api.pullpush.io/reddit/search/submission/?author=${queryInfo.value}&size=25&sort=desc`;
        const userPostsResponse = await axios.get(userPostsUrl, {
          timeout: 8000,
          headers: {
            'User-Agent': 'ViewDeletedReddit/1.0'
          }
        });

        if (userPostsResponse.data && userPostsResponse.data.data) {
          userPostsResponse.data.data.forEach((post: any) => {
            posts.push({
              id: post.id,
              title: post.title || '[Recovered Post]',
              selftext: post.selftext || '',
              author: post.author,
              created_utc: post.created_utc,
              subreddit: post.subreddit,
              url: `https://reddit.com/r/${post.subreddit}/comments/${post.id}/`,
              score: post.score || 0,
              num_comments: post.num_comments || 0,
              permalink: post.permalink || `/r/${post.subreddit}/comments/${post.id}/`,
              is_deleted: post.author === '[deleted]' || !post.selftext,
              source: 'pushshift'
            });
          });
        }

        const userCommentsUrl = `https://api.pullpush.io/reddit/search/comment/?author=${queryInfo.value}&size=50&sort=desc`;
        const userCommentsResponse = await axios.get(userCommentsUrl, {
          timeout: 8000,
          headers: {
            'User-Agent': 'ViewDeletedReddit/1.0'
          }
        });

        if (userCommentsResponse.data && userCommentsResponse.data.data) {
          userCommentsResponse.data.data.forEach((comment: any) => {
            comments.push({
              id: comment.id,
              body: comment.body || '[Recovered comment]',
              author: comment.author,
              created_utc: comment.created_utc,
              subreddit: comment.subreddit,
              score: comment.score || 0,
              parent_id: comment.parent_id,
              link_id: comment.link_id,
              permalink: comment.permalink || `/r/${comment.subreddit}/comments/${comment.link_id}/_/${comment.id}/`,
              is_deleted: comment.author === '[deleted]' || !comment.body,
              source: 'pushshift'
            });
          });
        }

      } catch (userError) {
        console.log('User data retrieval failed:', userError.message);
      }
    }

  } catch (error) {
    console.error('Alternative archives query error:', error);
  }

  return { posts, comments };
}

// Query Wayback Machine for archived Reddit content
async function queryWaybackMachine(queryInfo: ReturnType<typeof parseQuery>): Promise<{ posts: RedditPost[]; comments: RedditComment[] }> {
  const posts: RedditPost[] = [];
  const comments: RedditComment[] = [];
  
  try {
    if (queryInfo.type === 'url') {
      // Query Wayback Machine for archived versions of the Reddit URL
      const waybackUrl = `https://web.archive.org/cdx/search/cdx?url=${encodeURIComponent(queryInfo.value)}&output=json&limit=10`;
      
      const response = await axios.get(waybackUrl, {
        timeout: 10000
      });
      
      if (response.data && response.data.length > 1) {
        // Skip the header row and process results
        const archives = response.data.slice(1);
        
        for (const archive of archives.slice(0, 3)) { // Limit to 3 most recent archives
          const timestamp = archive[1];
          const archivedUrl = `https://web.archive.org/web/${timestamp}/${queryInfo.value}`;
          
          // Create a mock post entry for the archived URL
          posts.push({
            id: `wayback_${timestamp}`,
            title: `Archived Reddit Post (${new Date(timestamp.slice(0, 4) + '-' + timestamp.slice(4, 6) + '-' + timestamp.slice(6, 8)).toLocaleDateString()})`,
            selftext: `This post was archived by the Wayback Machine. View the archived version at: ${archivedUrl}`,
            author: '[archived]',
            created_utc: parseInt(timestamp),
            subreddit: queryInfo.subreddit || 'unknown',
            url: archivedUrl,
            score: 0,
            num_comments: 0,
            permalink: archivedUrl,
            is_deleted: true,
            source: 'wayback'
          });
        }
      }
    }
  } catch (error) {
    console.error('Wayback Machine API error:', error);
  }
  
  return { posts, comments };
}

// Mock data generator for demonstration (since real APIs may be unavailable)
function generateMockData(queryInfo: ReturnType<typeof parseQuery>): { posts: RedditPost[]; comments: RedditComment[] } {
  const posts: RedditPost[] = [];
  const comments: RedditComment[] = [];
  
  if (queryInfo.type === 'url') {
    // Generate mock deleted post
    posts.push({
      id: 'mock_post_1',
      title: '[RECOVERED] Example deleted Reddit post',
      selftext: 'This is an example of what a recovered deleted post might look like. The original content was removed by the user or moderators, but was preserved in our archives.',
      author: '[deleted]',
      created_utc: Date.now() / 1000 - 86400, // 1 day ago
      subreddit: queryInfo.subreddit || 'AskReddit',
      url: queryInfo.value,
      score: 42,
      num_comments: 15,
      permalink: `/r/${queryInfo.subreddit || 'AskReddit'}/comments/${queryInfo.postId}/`,
      is_deleted: true,
      source: 'pushshift'
    });
    
    // Generate mock deleted comments
    for (let i = 1; i <= 3; i++) {
      comments.push({
        id: `mock_comment_${i}`,
        body: `[RECOVERED] This is an example of a recovered deleted comment #${i}. The original comment was removed but preserved in archives.`,
        author: i === 1 ? '[deleted]' : `user${i}`,
        created_utc: Date.now() / 1000 - (3600 * i), // Hours ago
        subreddit: queryInfo.subreddit || 'AskReddit',
        score: 10 - i,
        parent_id: i === 1 ? `t3_${queryInfo.postId}` : `t1_mock_comment_${i-1}`,
        link_id: `t3_${queryInfo.postId}`,
        permalink: `/r/${queryInfo.subreddit || 'AskReddit'}/comments/${queryInfo.postId}/_/mock_comment_${i}/`,
        is_deleted: i <= 2,
        source: 'pushshift'
      });
    }
  } else {
    // Generate mock user data
    for (let i = 1; i <= 2; i++) {
      posts.push({
        id: `mock_user_post_${i}`,
        title: `[RECOVERED] Deleted post by u/${queryInfo.value} #${i}`,
        selftext: `This is an example of a recovered deleted post by user ${queryInfo.value}. Content was preserved before deletion.`,
        author: '[deleted]',
        created_utc: Date.now() / 1000 - (86400 * i),
        subreddit: `subreddit${i}`,
        url: `https://reddit.com/r/subreddit${i}/comments/example${i}/`,
        score: 25 - (i * 5),
        num_comments: 8 - i,
        permalink: `/r/subreddit${i}/comments/example${i}/`,
        is_deleted: true,
        source: 'pushshift'
      });
    }
    
    for (let i = 1; i <= 4; i++) {
      comments.push({
        id: `mock_user_comment_${i}`,
        body: `[RECOVERED] Deleted comment by u/${queryInfo.value}: This is example content #${i} that was preserved before deletion.`,
        author: '[deleted]',
        created_utc: Date.now() / 1000 - (7200 * i),
        subreddit: `subreddit${i % 2 + 1}`,
        score: 15 - (i * 2),
        parent_id: `t3_example${i}`,
        link_id: `t3_example${i}`,
        permalink: `/r/subreddit${i % 2 + 1}/comments/example${i}/_/mock_user_comment_${i}/`,
        is_deleted: true,
        source: 'pushshift'
      });
    }
  }
  
  return { posts, comments };
}

// Main API handler
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { query } = body;
    
    if (!query || typeof query !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Query parameter is required and must be a string'
      }, { status: 400 });
    }
    
    const queryInfo = parseQuery(query);
    
    // Run queries in parallel
    const [alternativeResults, waybackResults] = await Promise.all([
      queryAlternativeArchives(queryInfo),
      queryWaybackMachine(queryInfo)
    ]);

    // Only include mock data if no real results found
    let mockResults = { posts: [], comments: [] };
    if (alternativeResults.posts.length === 0 && alternativeResults.comments.length === 0 &&
        waybackResults.posts.length === 0 && waybackResults.comments.length === 0) {
      mockResults = generateMockData(queryInfo);
    }

    // Combine and deduplicate results
    const allPosts = [...alternativeResults.posts, ...waybackResults.posts, ...mockResults.posts];
    const allComments = [...alternativeResults.comments, ...waybackResults.comments, ...mockResults.comments];
    
    // Remove duplicates based on ID
    const uniquePosts = allPosts.filter((post, index, self) => 
      index === self.findIndex(p => p.id === post.id)
    );
    const uniqueComments = allComments.filter((comment, index, self) => 
      index === self.findIndex(c => c.id === comment.id)
    );
    
    const response: APIResponse = {
      success: true,
      data: {
        posts: uniquePosts,
        comments: uniqueComments,
        total_found: uniquePosts.length + uniqueComments.length,
        query_type: queryInfo.type,
        query_value: queryInfo.value
      }
    };
    
    return NextResponse.json(response);
    
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 });
  }
}

// Handle GET requests for testing
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('query');
  
  if (!query) {
    return NextResponse.json({
      success: false,
      error: 'Query parameter is required'
    }, { status: 400 });
  }
  
  // Redirect to POST handler
  return POST(new NextRequest(request.url, {
    method: 'POST',
    body: JSON.stringify({ query }),
    headers: { 'Content-Type': 'application/json' }
  }));
}
