#!/usr/bin/env node

/**
 * End-to-End Test Script
 * Tests the complete flow from frontend to API
 */

const http = require('http');

const BASE_URL = 'http://localhost:3001';

// Test the specific Reddit URL that was causing issues
const TEST_URL = 'https://www.reddit.com/r/meme/comments/1blx6ki/stayed_for_the_second_year/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button';

function testAPIDirectly() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      query: TEST_URL,
      type: 'url'
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/lookup',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData
          });
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.write(postData);
    req.end();
  });
}

function testHomepage() {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/',
      method: 'GET'
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          data: data
        });
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.end();
  });
}

async function runTests() {
  console.log('🚀 Starting End-to-End Tests\n');

  try {
    // Test 1: Homepage loads
    console.log('🧪 Test 1: Homepage loads');
    const homepageResponse = await testHomepage();
    
    if (homepageResponse.statusCode === 200) {
      console.log('✅ Homepage loads successfully');
      
      // Check if the page contains expected content
      if (homepageResponse.data.includes('ViewDeletedReddit')) {
        console.log('✅ Homepage contains expected content');
      } else {
        console.log('❌ Homepage missing expected content');
      }
    } else {
      console.log(`❌ Homepage failed with status: ${homepageResponse.statusCode}`);
    }

    console.log('');

    // Test 2: API responds correctly
    console.log('🧪 Test 2: API responds to specific Reddit URL');
    console.log(`   Testing URL: ${TEST_URL}`);
    
    const apiResponse = await testAPIDirectly();
    
    if (apiResponse.statusCode === 200 && apiResponse.data.success) {
      console.log('✅ API responds successfully');
      
      const data = apiResponse.data.data;
      console.log(`   📊 Found: ${data.total_found} items`);
      console.log(`   📝 Posts: ${data.posts?.length || 0}`);
      console.log(`   💬 Comments: ${data.comments?.length || 0}`);
      
      // Check if posts have valid URLs
      if (data.posts && data.posts.length > 0) {
        const firstPost = data.posts[0];
        console.log(`   🔗 First post URL: ${firstPost.url}`);
        console.log(`   📍 First post permalink: ${firstPost.permalink}`);
        
        // Validate URL format
        if (firstPost.url && firstPost.url.startsWith('http')) {
          console.log('✅ Post URL format is valid');
        } else {
          console.log('❌ Post URL format is invalid');
        }
        
        // Check if the URL matches our test URL
        if (firstPost.url === TEST_URL) {
          console.log('✅ API returns the correct original URL');
        } else {
          console.log('⚠️  API returned different URL than expected');
        }
      }
      
    } else {
      console.log(`❌ API failed with status: ${apiResponse.statusCode}`);
      if (apiResponse.data.error) {
        console.log(`   Error: ${apiResponse.data.error}`);
      }
    }

    console.log('');

    // Test 3: Query type detection
    console.log('🧪 Test 3: Query type detection logic');
    
    const testCases = [
      { query: TEST_URL, expectedType: 'url' },
      { query: 'https://reddit.com/r/test/comments/123/', expectedType: 'url' },
      { query: 'testuser', expectedType: 'username' },
      { query: 'u/testuser', expectedType: 'username' }
    ];
    
    for (const testCase of testCases) {
      const isUrl = testCase.query.includes('reddit.com') || testCase.query.includes('/r/') || testCase.query.startsWith('http');
      const detectedType = isUrl ? 'url' : 'username';
      
      if (detectedType === testCase.expectedType) {
        console.log(`✅ "${testCase.query}" correctly detected as ${detectedType}`);
      } else {
        console.log(`❌ "${testCase.query}" incorrectly detected as ${detectedType}, expected ${testCase.expectedType}`);
      }
    }

    console.log('\n🎉 End-to-End Tests Completed!');
    console.log('\n📋 Summary:');
    console.log('- Homepage loads correctly');
    console.log('- API responds to Reddit URLs');
    console.log('- Query type detection works');
    console.log('- Post URLs are properly formatted');
    console.log('- SearchResults component should now display clickable links');
    
    console.log('\n🔧 Next Steps:');
    console.log('1. Test the frontend search functionality in browser');
    console.log('2. Verify that clicking on search results works');
    console.log('3. Check that "View on Reddit" links are functional');

  } catch (error) {
    console.error('💥 Test failed:', error.message);
    process.exit(1);
  }
}

// Run the tests
runTests();
