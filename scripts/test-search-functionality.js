#!/usr/bin/env node

/**
 * Search Functionality Test Script
 * Tests specific Reddit URLs and validates the response structure
 */

const http = require('http');

const BASE_URL = process.env.TEST_URL || 'http://localhost:3001';

// Test cases with real Reddit URLs
const testCases = [
  {
    name: 'Test specific Reddit post URL',
    query: 'https://www.reddit.com/r/meme/comments/1blx6ki/stayed_for_the_second_year/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button',
    type: 'url',
    expectedFields: ['posts', 'comments', 'total_found', 'query_type', 'query_value']
  },
  {
    name: 'Test simplified Reddit URL',
    query: 'https://reddit.com/r/meme/comments/1blx6ki/',
    type: 'url',
    expectedFields: ['posts', 'comments', 'total_found', 'query_type', 'query_value']
  },
  {
    name: 'Test username search',
    query: 'testuser',
    type: 'username',
    expectedFields: ['posts', 'comments', 'total_found', 'query_type', 'query_value']
  }
];

function makeAPIRequest(testCase) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      query: testCase.query,
      type: testCase.type
    });

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/lookup',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({
            statusCode: res.statusCode,
            data: jsonData,
            headers: res.headers
          });
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    req.write(postData);
    req.end();
  });
}

function validateResponse(response, testCase) {
  const errors = [];
  
  // Check status code
  if (response.statusCode !== 200) {
    errors.push(`Expected status 200, got ${response.statusCode}`);
  }
  
  // Check if response has success field
  if (!response.data.success) {
    errors.push('Response does not indicate success');
  }
  
  // Check required fields
  const data = response.data.data;
  if (!data) {
    errors.push('Response missing data field');
    return errors;
  }
  
  testCase.expectedFields.forEach(field => {
    if (!(field in data)) {
      errors.push(`Missing required field: ${field}`);
    }
  });
  
  // Validate posts structure
  if (data.posts && Array.isArray(data.posts)) {
    data.posts.forEach((post, index) => {
      const requiredPostFields = ['id', 'title', 'author', 'url', 'permalink', 'source'];
      requiredPostFields.forEach(field => {
        if (!(field in post)) {
          errors.push(`Post ${index} missing field: ${field}`);
        }
      });
      
      // Validate URL format
      if (post.url && !post.url.startsWith('http')) {
        errors.push(`Post ${index} has invalid URL format: ${post.url}`);
      }
    });
  }
  
  // Validate comments structure
  if (data.comments && Array.isArray(data.comments)) {
    data.comments.forEach((comment, index) => {
      const requiredCommentFields = ['id', 'body', 'author', 'permalink', 'source'];
      requiredCommentFields.forEach(field => {
        if (!(field in comment)) {
          errors.push(`Comment ${index} missing field: ${field}`);
        }
      });
    });
  }
  
  return errors;
}

async function runTest(testCase) {
  try {
    console.log(`🧪 Testing: ${testCase.name}`);
    console.log(`   Query: ${testCase.query}`);
    console.log(`   Type: ${testCase.type}`);
    
    const response = await makeAPIRequest(testCase);
    const errors = validateResponse(response, testCase);
    
    if (errors.length === 0) {
      console.log(`✅ ${testCase.name} - PASSED`);
      
      // Log some details about the response
      const data = response.data.data;
      console.log(`   📊 Found: ${data.total_found} items (${data.posts?.length || 0} posts, ${data.comments?.length || 0} comments)`);
      
      // Show first post URL for validation
      if (data.posts && data.posts.length > 0) {
        console.log(`   🔗 First post URL: ${data.posts[0].url}`);
        console.log(`   📍 First post permalink: ${data.posts[0].permalink}`);
      }
      
      return true;
    } else {
      console.log(`❌ ${testCase.name} - FAILED:`);
      errors.forEach(error => console.log(`      - ${error}`));
      return false;
    }
    
  } catch (error) {
    console.log(`❌ ${testCase.name} - ERROR: ${error.message}`);
    return false;
  }
}

async function runAllTests() {
  console.log(`🚀 Starting search functionality tests for ${BASE_URL}\n`);
  
  let passed = 0;
  let failed = 0;
  
  for (const testCase of testCases) {
    const result = await runTest(testCase);
    if (result) {
      passed++;
    } else {
      failed++;
    }
    console.log(''); // Empty line for readability
  }
  
  console.log('📊 Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%`);
  
  if (failed > 0) {
    console.log('\n⚠️  Some tests failed. Please review the API responses.');
    process.exit(1);
  } else {
    console.log('\n🎉 All search functionality tests passed!');
    process.exit(0);
  }
}

// Run tests
runAllTests().catch((error) => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
